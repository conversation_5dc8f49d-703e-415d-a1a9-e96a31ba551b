# 网关安全配置说明

## 问题描述
网关中的敏感端点（如 `/actuator/gateway/routes`, `/swagger-resources`, `/actuator/heapdump` 等）可以在没有鉴权的情况下访问，存在安全风险。

## 解决方案

### 1. 代码层面的修改（已完成）
- 在 `AuthFilter` 中添加了敏感端点强制鉴权逻辑
- 创建了 `SensitiveEndpointProperties` 配置类来管理敏感端点列表
- 即使这些端点在白名单中，也会强制进行鉴权

### 2. Nacos配置中心配置（需要手动操作）

#### 在Nacos管理界面中找到网关配置文件（通常是 `qx-gateway-dev.yml`），添加以下配置：

```yaml
# 安全配置
security:
  # 白名单配置 - 建议移除或限制actuator相关配置
  ignore:
    whites:
      - /auth/**
      - /code
      - /favicon.ico
      # 移除以下危险配置：
      # - /actuator/**
      # - /swagger-resources/**
  
  # 敏感端点配置（新增）
  sensitive:
    # 强制需要鉴权的敏感端点
    endpoints:
      - /actuator/gateway/routes
      - /actuator/heapdump
      - /actuator/nacosdiscovery
      - /actuator/env
      - /actuator/configprops
      - /actuator/beans
      - /actuator/mappings
      - /actuator/shutdown
      - /swagger-resources
    # 允许不鉴权的端点（如健康检查）
    allowedEndpoints:
      - /actuator/health
      - /actuator/info
```

### 3. 验证配置

配置完成后，重启网关服务，然后测试：

1. **无token访问敏感端点应该返回401**：
   ```bash
   curl http://127.0.0.1:8080/actuator/gateway/routes
   # 应该返回: {"code":401,"msg":"令牌不能为空"}
   ```

2. **有效token访问敏感端点应该正常**：
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" http://127.0.0.1:8080/actuator/gateway/routes
   # 应该返回正常的路由信息
   ```

3. **健康检查端点应该可以正常访问**：
   ```bash
   curl http://127.0.0.1:8080/actuator/health
   # 应该返回健康状态信息
   ```

### 4. 日志监控

修改后的代码会在日志中记录敏感端点的访问尝试：
```
WARN [敏感端点访问]请求路径:/actuator/gateway/routes, 需要鉴权
```

可以通过监控这些日志来发现潜在的安全威胁。

## 注意事项

1. **配置优先级**：代码中的强制鉴权逻辑优先级高于白名单配置
2. **动态配置**：使用了 `@RefreshScope` 注解，支持Nacos配置的动态刷新
3. **扩展性**：可以通过Nacos配置轻松添加或移除敏感端点
4. **向后兼容**：如果没有配置敏感端点列表，会使用默认的端点列表

## 建议的最佳实践

1. **定期审查白名单**：确保白名单中没有敏感端点
2. **最小权限原则**：只开放必要的端点
3. **监控访问日志**：定期检查敏感端点的访问情况
4. **使用HTTPS**：在生产环境中使用HTTPS协议
5. **定期更新**：根据安全需求定期更新敏感端点列表
