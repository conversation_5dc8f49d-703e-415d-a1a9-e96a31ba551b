# Tomcat
server:
  port: 9200
feign:
  client:
    config:
      default:
        connectTimeout: 10000  # 明确覆盖
        readTimeout: 20000
# Spring
spring:
  application:
    # 应用名称
    name: qx-auth
    serveraddr: 127.0.0.1:8848
    groupId: DEFAULT_GROUP
    namespace: ebd844ab-4e0e-4a08-8e6e-c1ad74d0472a
#    namespace: 16f694c6-1d23-412b-b98f-0a640de80567
    access-key: aXNob3dhZG1pbg==
    secret-key: $2a$10$VvSBM8Hon7aDJqKJjGfS6.5YcUl9LoJTYg109GXmszWSipA6Z21/m
    username: nacos
    password: iShow2024LidyBN.!
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${spring.application.serveraddr}
        groupId: ${spring.application.groupId}
        namespace: ${spring.application.namespace}
        username: ${spring.application.username}
        password: ${spring.application.password}
        access-key: ${spring.application.access-key}
        secret-key: ${spring.application.secret-key}
      config:
        # 配置中心地址
        server-addr: ${spring.application.serveraddr}
        groupId: ${spring.application.groupId}
        namespace: ${spring.application.namespace}
        username: ${spring.application.username}
        password: ${spring.application.password}
        access-key: ${spring.application.access-key}
        secret-key: ${spring.application.secret-key}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
