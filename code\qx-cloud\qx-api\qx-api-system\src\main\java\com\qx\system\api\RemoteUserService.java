package com.qx.system.api;

import com.qx.common.core.web.domain.entity.SysUser;
import com.qx.system.api.config.FeignConfig;
import com.qx.system.api.factory.RemoteUserFallbackFactory;
import com.qx.common.core.web.domain.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import com.qx.common.core.constant.SecurityConstants;
import com.qx.common.core.constant.ServiceNameConstants;
import com.qx.common.core.domain.R;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class,
        configuration = FeignConfig.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过token查询用户信息
     *
     * @param token token
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/token/{token}")
    public R<LoginUser> getUserToken(@PathVariable("token") String token, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过token查询用户信息
     *
     * @param token token
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/admintoken/{token}")
    public R<LoginUser> getUserByToken(@PathVariable("token") String token, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
