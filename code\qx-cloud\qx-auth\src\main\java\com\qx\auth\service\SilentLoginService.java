package com.qx.auth.service;

import com.qx.common.core.exception.ServiceException;
import com.qx.common.core.utils.AssertUtil;
import mypackage.WsServiceImplServiceLocator;
import mypackage.WsServiceImplServiceSoapBindingStub;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.StringReader;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class SilentLoginService {

    @Autowired
    private SysPasswordService passwordService;


    @Value("${chinamobile.noninductive}")
    private String noninductive = "";

    @Value("${chinamobile.appcode}")
    private String appcode = "";

    public String checkSmsCode(String username, String phone, String code, String ip) {
        String result = "";
        try {


            WsServiceImplServiceSoapBindingStub stub
                    = new WsServiceImplServiceSoapBindingStub(new URL(noninductive), new WsServiceImplServiceLocator());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            String xmlData = "<ROOT>" +
                    "<HEAD>" +
                    "<TIMESTAMP>" + sdf.format(new Date())
                    + "</TIMESTAMP>" +
                    "</HEAD>" +
                    "<BODY>" +
                    "<USERCODE>" + username + "</USERCODE>" +
                    "<PHONE>" + phone + "</PHONE>" +
                    "<SMSCODE>" + code + "</SMSCODE>" +
                    "</BODY>" +
                    "</ROOT>";
            String str = stub.checkSmsCode(xmlData);
            System.out.println("xmlData:" + xmlData + "result:" + str);
            AssertUtil.state((resultXml(str, "SUCC")).equals("0"), "登录4A：" + resultXml(str, "ERRDESC"));

            xmlData = "<ROOT>" +
                    "<HEAD>" +
                    "<TIMESTAMP>" + sdf.format(new Date())
                    + "</TIMESTAMP>" +
                    "</HEAD>" +
                    "<BODY>" +
                    "<USERCODE>" + username + "</USERCODE>" +
                    "<SYSTEMACCOUNT>" + username + "</SYSTEMACCOUNT>" +
                    "<SYSTEMCODE>" + appcode + "</SYSTEMCODE>" +
                    "</BODY>" +
                    "</ROOT>";
            str = stub.getTicket(xmlData);
            System.out.println("xmlData:" + xmlData + "result:" + str);
            AssertUtil.state((resultXml(str, "SUCC")).equals("0"), "登录4A：" + resultXml(str, "ERRDESC"));
            result = resultXml(str, "TICKET");

            String date = sdf.format(new Date());
            xmlData = "<ROOT>" +
                    "<HEAD>" +
                    "<TIMESTAMP>" + date
                    + "</TIMESTAMP>" +
                    "</HEAD>" +
                    "<BODY>" +
                    "<USERCODE>" + username + "</USERCODE>" +
                    "<SYSTEMCODE>" + appcode + "</SYSTEMCODE>" +
                    "<SUBACCOUNTNAME>" + username + "</SUBACCOUNTNAME>" +
                    "<LOGINTIME>" + date + "</LOGINTIME>" +
                    "<CLIENTIP>"+ip+"</CLIENTIP>" +
                    "<REMARK>"+ip+"</REMARK>" +
                    "</BODY>" +
                    "</ROOT>";
            str = stub.saveLog(xmlData);
            System.out.println("xmlData:" + xmlData + "result:" + str);
            AssertUtil.state((resultXml(str, "SUCC")).equals("0"), "登录4A：" + resultXml(str, "ERRDESC"));


        } catch (ServiceException exT) {

            throw exT;
        } catch (Exception ex) {
            result = "1";
        }
        return result;
    }


    public int checkLogin(String username, String password) {
        int result = 0;
        try {
            passwordService.validate(username);

            WsServiceImplServiceSoapBindingStub stub
                    = new WsServiceImplServiceSoapBindingStub(new URL(noninductive), new WsServiceImplServiceLocator());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            String xmlData = "<ROOT>" +
                    "<HEAD>" +
                    "<TIMESTAMP>" + sdf.format(new Date())
                    + "</TIMESTAMP>" +
                    "</HEAD>" +
                    "<BODY>" +
                    "<USERCODE>" + username + "</USERCODE>" +
                    "</BODY>" +
                    "</ROOT>";
            String str = stub.getSaltByUserCode(xmlData);
            System.out.println("发送:" + xmlData + "返回:" + str);
            AssertUtil.state((resultXml(str, "SUCC")).equals("0"), "登录4A：" + resultXml(str, "ERRDESC"));
            String SALT = resultXml(str, "SALT");

            xmlData = "<ROOT>" +
                    "<HEAD>" +
                    "<TIMESTAMP>" + sdf.format(new Date()) + "</TIMESTAMP>" +
                    "</HEAD>" +
                    "<BODY>" +
                    "<USERCODE>" + username + "</USERCODE>" +
                    "<PASSWORD>" + password + "</PASSWORD>" +
                    "<SYSTEMCODE>" + appcode + "</SYSTEMCODE>" +
                    "<SALT>" + password + SALT + "</SALT>" +
                    "</BODY>" +
                    "</ROOT>";
            str = stub.checkUser(xmlData);
            System.out.println("发送:" + xmlData + "返回:" + str);
            AssertUtil.state((resultXml(str, "SUCC")).equals("0"), "登录4A：" + resultXml(str, "ERRDESC"));

            xmlData = "<ROOT>" +
                    "<HEAD>" +
                    "<TIMESTAMP>" + sdf.format(new Date()) + "</TIMESTAMP>" +
                    "</HEAD>" +
                    "<BODY>" +
                    "<USERCODE>" + username + "</USERCODE>" +
                    "<SYSTEMCODE>" + appcode + "</SYSTEMCODE>" +
                    "<SUBACCOUNTNAME>" + username + "</SUBACCOUNTNAME>" +
                    "</BODY>" +
                    "</ROOT>";
            str = stub.applySmsCode(xmlData);
            System.out.println("发送:" + xmlData + "返回:" + str);
            AssertUtil.state((resultXml(str, "SUCC")).equals("0"), "登录4A：" + resultXml(str, "ERRDESC"));
        } catch (ServiceException exT) {
            passwordService.validateto(username,exT.getMessage());
            throw exT;
        } catch (Exception ex) {
            result = 1;
        }
        return result;
    }


    private String resultXml(String xmlString, String name)
            throws ParserConfigurationException, IOException, SAXException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        // 禁用外部实体 (XXE) 处理
        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        factory.setXIncludeAware(false);
        factory.setExpandEntityReferences(false);

        DocumentBuilder builder = factory.newDocumentBuilder();
        InputSource inputSource = new InputSource(new StringReader(xmlString));
        Document document = builder.parse(inputSource);

        // 获取根节点
        Element root = document.getDocumentElement();

        // 获取指定 name 节点的值
        return root.getElementsByTagName(name).item(0).getTextContent();
    }

}
