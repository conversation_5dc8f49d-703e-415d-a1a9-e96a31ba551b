09:36:22.361 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:22.727 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:36:22.727 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:36:26.603 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
09:36:28.108 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
09:36:28.108 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
09:36:28.108 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
09:36:28.113 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
09:36:28.126 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
09:36:28.127 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:36:28.127 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
09:36:28.275 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:36:29.943 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:36:32.335 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
09:36:32.406 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:36:32.406 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:36:32.621 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
09:36:33.134 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 11.964 seconds (JVM running for 13.574)
09:36:33.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
09:36:33.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
09:36:33.153 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
09:36:34.048 [RMI TCP Connection(4)-192.163.221.46] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:02:07.888 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:02:08.202 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:02:08.202 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:02:12.057 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:02:13.689 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
10:02:13.689 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
10:02:13.689 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
10:02:13.695 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
10:02:13.709 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
10:02:13.710 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:02:13.711 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
10:02:13.877 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:02:15.417 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:02:17.712 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
10:02:17.752 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:02:17.753 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:02:17.958 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
10:02:18.587 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.106 seconds (JVM running for 13.225)
10:02:18.603 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
10:02:18.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
10:02:18.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
10:02:19.000 [RMI TCP Connection(2)-192.163.221.46] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:03:14.188 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:03:14.455 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:03:14.456 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:03:17.051 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:03:18.300 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
10:03:18.300 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
10:03:18.300 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
10:03:18.304 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
10:03:18.311 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
10:03:18.312 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:03:18.312 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
10:03:18.432 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:03:19.562 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:03:21.378 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
10:03:21.418 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:03:21.418 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:03:21.635 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
10:03:22.473 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 9.229 seconds (JVM running for 10.355)
10:03:22.494 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
10:03:22.496 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
10:03:22.497 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
10:03:23.553 [RMI TCP Connection(5)-192.163.221.46] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
