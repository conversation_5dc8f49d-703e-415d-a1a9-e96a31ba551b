package com.qx.gateway.config.properties;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 敏感端点配置
 * 这些端点即使在白名单中也需要进行鉴权
 *
 * <AUTHOR>
 */
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "security.sensitive")
public class SensitiveEndpointProperties
{
    /**
     * 敏感端点列表，这些端点强制需要鉴权
     */
    private List<String> endpoints = new ArrayList<>(Arrays.asList(
        "/actuator/gateway/routes",
        "/actuator/heapdump", 
        "/actuator/nacosdiscovery",
        "/actuator/env",
        "/actuator/configprops",
        "/actuator/beans",
        "/actuator/mappings",
        "/actuator/shutdown",
        "/swagger-resources"
    ));

    /**
     * 允许的actuator端点（不需要鉴权）
     */
    private List<String> allowedEndpoints = new ArrayList<>(Arrays.asList(
        "/actuator/health",
        "/actuator/info"
    ));

    public List<String> getEndpoints()
    {
        return endpoints;
    }

    public void setEndpoints(List<String> endpoints)
    {
        this.endpoints = endpoints;
    }

    public List<String> getAllowedEndpoints()
    {
        return allowedEndpoints;
    }

    public void setAllowedEndpoints(List<String> allowedEndpoints)
    {
        this.allowedEndpoints = allowedEndpoints;
    }
}
