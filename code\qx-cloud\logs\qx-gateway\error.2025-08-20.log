10:57:25.051 [reactor-http-nio-4] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/doc.html,异常信息:404 NOT_FOUND
10:57:25.108 [reactor-http-nio-4] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
10:57:33.448 [boundedElastic-315] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/code/v2/api-docs,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for qx-gen"
10:59:52.833 [reactor-http-nio-13] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/doc.html,异常信息:404 NOT_FOUND
10:59:52.864 [reactor-http-nio-13] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
10:59:54.527 [reactor-http-nio-13] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/doc.html,异常信息:404 NOT_FOUND
10:59:54.547 [reactor-http-nio-13] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
11:00:29.481 [boundedElastic-291] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/code/v2/api-docs,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for qx-gen"
11:01:47.426 [reactor-http-nio-2] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/doc.html,异常信息:404 NOT_FOUND
11:01:47.494 [reactor-http-nio-2] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
11:01:48.853 [reactor-http-nio-2] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/doc.html,异常信息:404 NOT_FOUND
11:01:48.871 [reactor-http-nio-2] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
11:02:12.193 [reactor-http-nio-4] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/swagger-ui/doc.html,异常信息:404 NOT_FOUND
11:02:12.217 [reactor-http-nio-4] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
15:06:24.579 [reactor-http-nio-2] ERROR c.q.g.f.AuthFilter - [unauthorizedResponse,128] - [鉴权异常处理]请求路径:/system/menu/getRouters
15:06:42.077 [reactor-http-nio-3] ERROR c.q.g.f.AuthFilter - [unauthorizedResponse,128] - [鉴权异常处理]请求路径:/system/menu/getRouters
16:41:49.749 [boundedElastic-111] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/api/home/<USER>"Unable to find instance for qx-modules-api"
