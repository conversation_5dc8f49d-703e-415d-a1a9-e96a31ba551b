<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="qx-common-core" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.8.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.14" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.16" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.15" level="project" />
    <orderEntry type="library" name="Maven: com.sun.mail:javax.mail:1.6.2" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:activation:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-openfeign:3.1.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-openfeign-core:3.1.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.7" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form-spring:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.7.12" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-core:11.10" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-slf4j:11.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-loadbalancer:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-loadbalancer:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.4.34" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor.addons:reactor-extra:3.4.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-cache:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: com.stoyanr:evictor:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:transmittable-thread-local:2.14.4" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper-spring-boot-starter:2.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-starter:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.13" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper-spring-boot-autoconfigure:2.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper:6.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:4.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.2.5.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.4.3.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.fastjson2:fastjson2:2.0.43" level="project" />
    <orderEntry type="library" name="Maven: io.jsonwebtoken:jjwt:0.9.1" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.bind:jaxb-api:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:javax.activation-api:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.12.0" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.13.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml:4.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi:4.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-math3:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml-schemas:4.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.virtuald:curvesapi:1.06" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.26.2" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:4.0.1" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.6.2" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.30" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel:2.2.5" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:4.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: org.ehcache:ehcache:3.10.8" level="project" />
    <orderEntry type="library" name="Maven: javax.cache:cache-api:1.1.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:jaxb-runtime:2.3.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:txw2:2.3.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.istack:istack-commons-runtime:3.0.12" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.activation:jakarta.activation:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:ooxml-schemas:1.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xmlbeans:xmlbeans:3.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-bootstrap:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-rsa:1.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcpkix-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcutil-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.33" level="project" />
  </component>
</module>