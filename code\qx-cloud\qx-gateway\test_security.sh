#!/bin/bash

# 网关安全测试脚本
# 用于验证敏感端点是否正确进行鉴权

GATEWAY_URL="http://127.0.0.1:8080"
TOKEN="YOUR_TOKEN_HERE"  # 请替换为有效的token

echo "=== 网关安全测试 ==="
echo "网关地址: $GATEWAY_URL"
echo ""

# 测试敏感端点（应该返回401）
echo "1. 测试敏感端点（无token，应该返回401）:"
echo "-------------------------------------------"

endpoints=(
    "/actuator/gateway/routes"
    "/actuator/heapdump"
    "/actuator/nacosdiscovery"
    "/actuator/env"
    "/swagger-resources"
)

for endpoint in "${endpoints[@]}"; do
    echo -n "测试 $endpoint: "
    response=$(curl -s -w "%{http_code}" -o /dev/null "$GATEWAY_URL$endpoint")
    if [ "$response" = "401" ]; then
        echo "✅ 正确拦截 (HTTP $response)"
    else
        echo "❌ 未正确拦截 (HTTP $response)"
    fi
done

echo ""
echo "2. 测试允许的端点（无token，应该返回200）:"
echo "-------------------------------------------"

allowed_endpoints=(
    "/actuator/health"
    "/actuator/info"
)

for endpoint in "${allowed_endpoints[@]}"; do
    echo -n "测试 $endpoint: "
    response=$(curl -s -w "%{http_code}" -o /dev/null "$GATEWAY_URL$endpoint")
    if [ "$response" = "200" ]; then
        echo "✅ 正常访问 (HTTP $response)"
    else
        echo "⚠️  可能有问题 (HTTP $response)"
    fi
done

echo ""
echo "3. 测试敏感端点（有token，需要手动设置TOKEN变量）:"
echo "-------------------------------------------"

if [ "$TOKEN" = "YOUR_TOKEN_HERE" ]; then
    echo "⚠️  请先设置有效的TOKEN变量再运行此测试"
else
    for endpoint in "${endpoints[@]}"; do
        echo -n "测试 $endpoint (with token): "
        response=$(curl -s -w "%{http_code}" -o /dev/null -H "Authorization: Bearer $TOKEN" "$GATEWAY_URL$endpoint")
        if [ "$response" = "200" ]; then
            echo "✅ 鉴权通过 (HTTP $response)"
        else
            echo "❌ 鉴权失败 (HTTP $response)"
        fi
    done
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "说明:"
echo "✅ 表示按预期工作"
echo "❌ 表示存在安全问题"
echo "⚠️  表示需要进一步检查"
