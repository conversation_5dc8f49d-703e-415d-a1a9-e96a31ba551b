package com.qx.system.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("token")
public class MobileToken {

    @ApiModelProperty("token")
    private String token;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getIsMobile() {
        return isMobile;
    }

    public void setIsMobile(Integer isMobile) {
        this.isMobile = isMobile;
    }

    public String getTimeMd5() {
        return timeMd5;
    }

    public void setTimeMd5(String timeMd5) {
        this.timeMd5 = timeMd5;
    }

    @ApiModelProperty("0:PC,1:手机,2:移动APP")
    private Integer isMobile;

    @ApiModelProperty("时间md5")
    private String timeMd5;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("当前时间戳")
    private String timestamp;

    @ApiModelProperty("签名")
    private String sign;


    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


}
