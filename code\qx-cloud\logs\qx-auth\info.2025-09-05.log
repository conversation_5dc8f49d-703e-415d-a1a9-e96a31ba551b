11:46:19.185 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:46:19.574 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:46:19.574 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:46:23.171 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:46:28.601 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
11:46:28.602 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
11:46:28.602 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
11:46:28.606 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
11:46:28.617 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
11:46:28.619 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:46:28.619 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
11:46:28.828 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:46:31.472 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:46:35.608 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
11:46:35.751 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:46:35.751 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:46:35.984 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.213:9200 register finished
11:46:36.468 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 18.123 seconds (JVM running for 19.771)
11:46:36.484 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
11:46:36.485 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
11:46:36.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
11:46:36.954 [RMI TCP Connection(8)-192.163.221.46] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:49:15.170 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:49:15.491 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:49:15.491 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:49:19.711 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:49:21.781 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
11:49:21.781 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
11:49:21.781 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
11:49:21.786 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
11:49:21.797 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
11:49:21.798 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:49:21.799 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
11:49:21.955 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:49:24.047 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:49:27.554 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
11:49:27.586 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:49:27.586 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:49:27.790 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.213:9200 register finished
11:49:28.281 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.025 seconds (JVM running for 15.47)
11:49:28.299 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
11:49:28.300 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
11:49:28.301 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
11:49:28.750 [RMI TCP Connection(5)-192.163.221.46] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:46:31.330 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:46:31.333 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
