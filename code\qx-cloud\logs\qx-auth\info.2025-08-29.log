10:31:46.838 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:31:47.246 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:31:47.246 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:31:52.571 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:31:54.406 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
10:31:54.406 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
10:31:54.407 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
10:31:54.411 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
10:31:54.422 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
10:31:54.424 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:31:54.424 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
10:31:54.558 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:31:55.982 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:32:00.020 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
10:32:00.074 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:32:00.074 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:32:00.270 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
10:32:01.709 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 16.724 seconds (JVM running for 17.75)
10:32:01.764 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
10:32:01.765 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
10:32:01.766 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
10:32:02.105 [RMI TCP Connection(4)-172.25.176.1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:24:58.288 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:24:58.682 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:24:58.682 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:25:02.623 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:25:04.179 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
11:25:04.179 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
11:25:04.179 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
11:25:04.184 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
11:25:04.199 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
11:25:04.201 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:25:04.201 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
11:25:04.334 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:25:05.766 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:25:09.619 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
11:25:09.698 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:25:09.698 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:25:09.912 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
11:25:11.414 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 15.029 seconds (JVM running for 16.298)
11:25:11.429 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
11:25:11.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
11:25:11.432 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
11:25:11.962 [RMI TCP Connection(4)-172.25.176.1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:21:28.803 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:21:28.808 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:37:02.578 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:37:02.898 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:37:02.899 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:37:07.486 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
18:37:09.392 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
18:37:09.393 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
18:37:09.393 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
18:37:09.401 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
18:37:09.420 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
18:37:09.421 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
18:37:09.422 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
18:37:09.608 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
18:37:11.389 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:37:15.636 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
18:37:15.674 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:37:15.675 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:37:15.878 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
18:37:17.405 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 16.754 seconds (JVM running for 17.913)
18:37:17.417 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
18:37:17.418 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
18:37:17.419 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
18:37:18.123 [RMI TCP Connection(8)-172.25.176.1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:39:57.065 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:39:57.325 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:39:57.326 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:40:00.742 [main] INFO  c.q.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
18:40:01.946 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
18:40:01.947 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
18:40:01.947 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
18:40:01.950 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
18:40:01.957 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9200"]
18:40:01.958 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
18:40:01.959 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
18:40:02.136 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
18:40:05.064 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:40:09.499 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-9200"]
18:40:09.551 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:40:09.551 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:40:09.763 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP qx-auth 192.168.0.53:9200 register finished
18:40:11.171 [main] INFO  c.q.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 16.027 seconds (JVM running for 17.118)
18:40:11.219 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth.yml, group=DEFAULT_GROUP
18:40:11.224 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth-dev.yml, group=DEFAULT_GROUP
18:40:11.226 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,131] - [Nacos Config] Listening config: dataId=qx-auth, group=DEFAULT_GROUP
18:40:11.297 [RMI TCP Connection(1)-172.25.176.1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
