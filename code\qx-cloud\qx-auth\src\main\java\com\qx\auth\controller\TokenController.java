package com.qx.auth.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.alibaba.nacos.api.common.Constants;
import com.qx.auth.form.LoginBodyToken;
import com.qx.common.core.web.domain.AjaxResult;
import com.qx.common.core.web.domain.entity.QxUserCustmoerTo;
import com.qx.system.api.model.MobileToken;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.qx.auth.form.LoginBody;
import com.qx.auth.form.RegisterBody;
import com.qx.auth.service.SysLoginService;
import com.qx.common.core.domain.R;
import com.qx.common.core.utils.JwtUtils;
import com.qx.common.core.utils.StringUtils;
import com.qx.common.security.auth.AuthUtil;
import com.qx.common.security.service.TokenService;
import com.qx.common.security.utils.SecurityUtils;
import com.qx.common.core.web.domain.model.LoginUser;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class TokenController {
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @PostMapping("/adminlogin")
    public AjaxResult adminlogin(@Validated @RequestBody LoginBody form) {
        // 用户登录
        return sysLoginService.login(form.getUsername(), form.getPassword(), form.getIsAdmin());
    }

    @PostMapping("/tokenlogin")
    public AjaxResult tokenlogin(@RequestBody LoginBodyToken token) {
        // 用户登录
        if (token.getIsAdmin() == null) {
            return sysLoginService.login(token);
        } else {
            return sysLoginService.adminlogin(token);
        }
    }

//    /**
//     * 后台短信验证码登录方法
//     *
//     * @param loginBody 登录信息
//     * @return 结果
//     */
//    @PostMapping("/verifycode")
//    public AjaxResult verifycode(@RequestBody LoginBody loginBody) {
//        //AjaxResult ajax = AjaxResult.success();
//        // 生成令牌
//        AjaxResult ajax = sysLoginService.verifycode(loginBody.getUsername(), loginBody.getCode());
//
//        //LoginUser loginUser =tokenService.getLoginUser()
//        return ajax;
//    }


//    @PostMapping("/login")
//    public AjaxResult login(@RequestBody MobileToken mobileToken)
//    {
//        // 用户登录
////        return sysLoginService.loginTo(form.getUsername(), form.getPassword());
//        //mobileToken.setToken("eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjo2NzgzNzY0MzU2ODkwNjI0LCJ1c2VyX2tleSI6ImQyNDhmNjNmLTE2OTQtNDAxZC05ZThkLTk3MGYwOWYzNjRiMCIsInVzZXJuYW1lIjoiUVhTWl9jaGVueGluIn0.13HOZ3a1pQCCTxg4y-vzuSM2xIDX8X7QTp5k68-XQdTLXnwI-ICMMhKSCDogWor1Q1tbuo6IBRCcqD82wQRexQ");
//        return sysLoginService.login(new MobileToken());
//    }

    @PostMapping("/onlinelogin")
    public AjaxResult onlinelogin( @RequestBody MobileToken mobileToken) {
        // 用户登录
        return sysLoginService.login(mobileToken);
    }

    @DeleteMapping("logout")
    public R<Object> logout(HttpServletRequest request) {
        try {
            String token = SecurityUtils.getToken(request);
            if (StringUtils.isNotEmpty(token)) {
                String username = JwtUtils.getUserName(token);
                // 删除用户缓存记录
                AuthUtil.logoutByToken(token);
                // 记录用户退出日志
                sysLoginService.logout(username);
            }
        }catch (Exception ex){

        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<Object> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<Object> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }
}
