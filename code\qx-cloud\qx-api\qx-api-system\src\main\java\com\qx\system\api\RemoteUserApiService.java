package com.qx.system.api;

import com.qx.common.core.constant.SecurityConstants;
import com.qx.common.core.constant.ServiceNameConstants;
import com.qx.common.core.domain.R;
import com.qx.common.core.web.domain.AjaxResult;
import com.qx.common.core.web.domain.entity.QxUserCustmoerTo;
import com.qx.system.api.factory.RemoteUserApiFallbackFactory;
import com.qx.system.api.model.MobileToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import  com.qx.system.api.config.RemoteUserApiConfiguration;
/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "RemoteUserApiService", value = ServiceNameConstants.MODULES_API, fallbackFactory = RemoteUserApiFallbackFactory.class,configuration = RemoteUserApiConfiguration.class)
public interface RemoteUserApiService
{
    /**
     * 通过用户token查询用户信息
     *
     * @param token 用户token
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/onlinelogin")
    public QxUserCustmoerTo getOnlineLogin(@RequestParam("token") String token,
                                           @RequestParam("isMobile") Integer isMobile,
                                           @RequestParam("timeMd5") String timeMd5,
                                           @RequestHeader(SecurityConstants.FROM_SOURCE) String source);



    @GetMapping("/onlineloginwd")
    public QxUserCustmoerTo getOnlineLoginWd(@RequestParam("userId") String userId,
                                           @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
