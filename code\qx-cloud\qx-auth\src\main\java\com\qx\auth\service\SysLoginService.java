package com.qx.auth.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qx.auth.form.LoginAdmin;
import com.qx.auth.form.LoginBodyToken;
import com.qx.common.core.utils.*;
import com.qx.common.core.utils.bean.BeanUtils;
import com.qx.common.core.utils.sign.Md5Utils;
import com.qx.common.core.web.domain.AjaxResult;
import com.qx.common.core.web.domain.entity.QxUserCustmoerTo;
import com.qx.common.core.web.domain.entity.QxUserCustomerVo;
import com.qx.common.core.web.domain.entity.QxUserVo;
import com.qx.common.core.web.domain.entity.SysUser;
import com.qx.common.core.web.domain.model.LoginMember;
import com.qx.common.security.service.TokenService;
import com.qx.system.api.RemoteUserApiService;
import com.qx.system.api.model.MobileToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.qx.common.core.constant.CacheConstants;
import com.qx.common.core.constant.Constants;
import com.qx.common.core.constant.SecurityConstants;
import com.qx.common.core.constant.UserConstants;
import com.qx.common.core.domain.R;
import com.qx.common.core.enums.UserStatus;
import com.qx.common.core.exception.ServiceException;
import com.qx.common.core.text.Convert;
import com.qx.common.core.utils.ip.IpUtils;
import com.qx.common.redis.service.RedisService;
import com.qx.common.security.utils.SecurityUtils;
import com.qx.system.api.RemoteUserService;

import com.qx.common.core.web.domain.model.LoginUser;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);
    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteUserApiService remoteUserApiService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SilentLoginService silentLoginService;

    @Autowired
    private TokenService tokenService;

    @Value("${rsa.privateKey}")
    private String privateKey;

    @Value("${rsa.is4A}")
    private String is4A;

    @Value("${rsa.adminUrl}")
    private String adminUrl;


    public AjaxResult login(MobileToken mobileToken) {

        //网大用户token生成
//        try {
//            long time= new Date().getTime();
//            String userId= ASEEncryptionUtil.encrypt(mobileToken.getUserId(), "6ba00487dbf84f88");
//            TreeMap<String, String> params = new TreeMap<>();
//            // 添加键值对到 TreeMap
//            params.put("userId", userId);
//            params.put("timestamp", String .valueOf(time));
//            String sign = "";
//            for (Map.Entry<String, String> entry : params.entrySet()) {
//                sign = sign.equals("") ? (entry.getKey() + "=" + entry.getValue()) : (sign + "&" + entry.getKey() + "=" + entry.getValue());
//            }
//            sign += "&secret=a19de0eb80013fb468fd2ee0cc672211";
//            sign = Md5Utils.hash(sign).toUpperCase();
//            System.out.println("timestamp="+time+"&userId="+userId+"&sign="+sign);
//        }catch (Exception ex){
//
//        }
        //业务指标 登录时延计算 start
        long loginDelayStartTime = System.currentTimeMillis();


        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor("api-user", Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        AjaxResult ajax = AjaxResult.success();
        // 查询用户信息
        QxUserCustmoerTo userResult = null;
        //网大用户登录
        if (mobileToken.getSign() != null && mobileToken.getUserId() != null && mobileToken.getTimestamp() != null) {
            System.out.println(JSON.toJSONString(mobileToken));
            // 获取当前时间
            Instant now = Instant.now();
            // 当前时间前 2 分钟的时间戳
            Instant twoMinutesAgo = now.minus(2, ChronoUnit.MINUTES);
            long wangdaTimestamp = Long.valueOf(mobileToken.getTimestamp());
            // 判断传入时间是否符合要求
//            if (Instant.ofEpochMilli(wangdaTimestamp).isBefore(twoMinutesAgo) || Instant.ofEpochMilli(wangdaTimestamp).isAfter(now)) {
//                System.out.println(mobileToken.getTimestamp() + "传入的时间不符合要求：它小于当前时间前 2 分钟，或者大于当前时间！" + now);
//                return AjaxResult.error("传入的时间不符合要求：它小于当前时间前 2 分钟，或者大于当前时间！");
//            }

            TreeMap<String, String> params = new TreeMap<>();
            // 添加键值对到 TreeMap
            params.put("userId", ServletUtils.urlEncode(mobileToken.getUserId()));
            params.put("timestamp", mobileToken.getTimestamp());
            String sign = "";
            for (Map.Entry<String, String> entry : params.entrySet()) {
                sign = sign.equals("") ? (entry.getKey() + "=" + entry.getValue()) : (sign + "&" + entry.getKey() + "=" + entry.getValue());
            }
            sign += "&secret=a19de0eb80013fb468fd2ee0cc672211";
            try {
                mobileToken.setUserId(ASEEncryptionUtil.decrypt(mobileToken.getUserId(), "6ba00487dbf84f88"));
            } catch (Exception ex) {

            }
            sign = Md5Utils.hash(sign).toUpperCase();
            if (sign.equals(mobileToken.getSign())) {
                userResult = remoteUserApiService.getOnlineLoginWd(mobileToken.getUserId(),
                        SecurityConstants.INNER);
            } else
                throw new ServiceException("网大用户鉴权失败");
            LoginAdmin loginAdmin = new LoginAdmin();
            loginAdmin.setIsAdmin(2);
            ajax.put("admin", JSON.toJSONString(loginAdmin));
        } else {
            userResult = remoteUserApiService.getOnlineLogin(mobileToken.getToken(),
                    mobileToken.getIsMobile(),
                    mobileToken.getTimeMd5(),
                    SecurityConstants.INNER);

            LoginAdmin loginAdmin = new LoginAdmin();
            if(userResult.getQxUserCustomerVo()!=null&&userResult.getQxUserCustomerVo().getIs4A()!=null&&userResult.getQxUserCustomerVo().getIs4A().equals(1)){
                loginAdmin.setIsAdmin(2);
            }else{
                loginAdmin.setIsAdmin(0);
            }

            loginAdmin.setAdminUrl(adminUrl);
            if (userResult.getQxUserCustomerVo() == null && userResult.getCode() == 200) {
                String md5Key = Md5Utils.hash(userResult.getJsonObject().getString("email"));
                String token=Md5Utils.hash(String.valueOf(KeyUtil.getInstance().nextId()));
                loginAdmin.setIsAdmin(1);
                redisService.setCacheObject(Constants.ADMIN_LOGIN + token,md5Key, 1, TimeUnit.DAYS);


                ajax.put("token", token);
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("code",0);
                jsonObject.put("result",userResult.getJsonObject());
                ajax.put("data", JSON.toJSONString(jsonObject));
                ajax.put("admin", JSON.toJSONString(loginAdmin));

                System.out.println(ajax);
                return ajax;
            }
            ajax.put("admin", JSON.toJSONString(loginAdmin));
        }


        QxUserCustomerVo qxUserCustomer = userResult.getQxUserCustomerVo();
        recordLogService.recordLogininforTo(qxUserCustomer.getAccount(), Constants.LOGIN_SUCCESS, "登录成功",qxUserCustomer.getProvinceName());
        LoginMember loginMember = new LoginMember();
        loginMember.setQxUserCustomer(qxUserCustomer);

        ajax.put("token", tokenService.createApiCustomerToken(loginMember).get("access_token"));
        ajax.put("data", JSON.toJSONString(userResult.getJsonObject()));
        System.out.println(ajax);

        return ajax;
    }



    public AjaxResult loginTo(String username, String password) {
        password = Md5Utils.hash(password);

        String md5Key = Md5Utils.hash(username);
//        System.out.println(md5Key);
//        username = AESUtil.encryptCBC(username, md5Key.substring(16),"****************");


        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("用户不存在或密码错误");
        }

        if (R.ERROR_CODE == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        QxUserVo user = userResult.getData().getUser();
        QxUserCustomerVo qxUserCustomer = new QxUserCustomerVo();

        BeanUtils.copyBeanProp(qxUserCustomer, user);
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        passwordService.validate(user, password);
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
        AjaxResult ajax = AjaxResult.success();
        LoginMember loginMember = new LoginMember();
        loginMember.setQxUserCustomer(qxUserCustomer);
        ajax.put("token", tokenService.createApiCustomerToken(loginMember).get("access_token"));
        ajax.put("data", "{}");
        System.out.println(ajax);
        return ajax;
    }

    /**
     * 登录验证token
     *
     * @param loginBodyToken token
     * @return 结果
     */
    public AjaxResult login(LoginBodyToken loginBodyToken) {
        AjaxResult ajax = AjaxResult.success();
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor("api-user", Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        String aeskey=null;

        //只有后端权限
        if(redisService.isKeyExists(Constants.ADMIN_LOGIN+loginBodyToken.getToken())){
            aeskey=redisService.getCacheObject(Constants.ADMIN_LOGIN+loginBodyToken.getToken());
        }else {
            LoginMember loginMember = tokenService.getApiCustomerUserTo(loginBodyToken.getToken());
            if (!StringUtils.isNull(loginMember)&&!StringUtils.isNull(loginMember.getQxUserCustomer())) {
                aeskey= loginMember.getQxUserCustomer().getAesKey();
            }
        }
        if(StringUtils.isNull(aeskey)){
            recordLogService.recordLogininfor(loginBodyToken.getToken(), Constants.LOGIN_FAIL, "前端token登录不存在");
            throw new ServiceException("前端token登录不存在");
        }
        R<LoginUser> userResult = remoteUserService.getUserToken(aeskey, SecurityConstants.INNER);
        if (R.ERROR_CODE == userResult.getCode()) {
            return AjaxResult.error(userResult.getMsg());
        }
        LoginUser userInfo = userResult.getData();
        QxUserVo user = userResult.getData().getUser();
        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogService.recordLogininfor(loginBodyToken.getToken(), Constants.LOGIN_FAIL, "前端token登录不存在");
            throw new ServiceException("服务端查询用户token登录不存在");
        }


        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(user.getAccount(), Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + user.getAccount() + " 已被删除");
        }
        recordLogService.recordLogininfor(user.getAccount(), Constants.LOGIN_SUCCESS, "登录成功");
        ajax.put("token", tokenService.createToken(userInfo).get("access_token"));
//        ajax.put("user", user);
//        ajax.put("roles", userResult.getData().);
//        ajax.put("permissions", permissions);
        ajax.put("url", userResult.getData().getUrl());
        return ajax;
    }


    /**
     * 登录验证token
     *
     * @param loginBodyToken token
     * @return 结果
     */
    public AjaxResult adminlogin(LoginBodyToken loginBodyToken) {
        AjaxResult ajax = AjaxResult.success();
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor("api-user", Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        R<LoginUser> userResult = remoteUserService.getUserByToken(loginBodyToken.getToken(), SecurityConstants.INNER);
        LoginUser userInfo = userResult.getData();
        QxUserVo user = userResult.getData().getUser();

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(user.getAccount(), Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + user.getAccount() + " 已被删除");
        }




        if (R.ERROR_CODE == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(user.getAccount(), Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + user.getAccount() + " 已被删除");
        }
        recordLogService.recordLogininfor(user.getAccount(), Constants.LOGIN_SUCCESS, "登录成功");
        ajax.put("token", tokenService.createToken(userInfo).get("access_token"));
//        ajax.put("user", user);
//        ajax.put("roles", userResult.getData().);
//        ajax.put("permissions", permissions);
        ajax.put("url", userResult.getData().getUrl());
        return ajax;
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param code     验证码
     * @return 结果
     */
    public AjaxResult verifycode(String username, String code) {
        AjaxResult ajax = AjaxResult.success();
        LoginUser loginUser = redisService.getCacheObject(username + "check");
        AssertUtil.state(loginUser != null, "验证码已过期！");
        final String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        String result = silentLoginService.checkSmsCode(username, loginUser.getUser().getPhone(), code, LogUtils.getBlock(ip));
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
        loginUser.setTicket(result);
        if (!result.equals("1")) {
            ajax.put("token", tokenService.createToken(loginUser).get("access_token"));
            ajax.put("url", loginUser.getUrl());
        }
        return ajax;
    }

    /**
     * 登录
     */
    public AjaxResult login(String username, String password, boolean isAdmin) {
        AjaxResult ajax = AjaxResult.success();
        password =  RsaUtils.decryptByPrivateKey(privateKey, password);
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("用户不存在或密码错误");
        }

        if (R.ERROR_CODE == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }


        LoginUser userInfo = userResult.getData();
        QxUserVo user = userResult.getData().getUser();

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }

//        //管理员账号登录4A系统
        if (is4A.equals("0") && user.getAccountType().equals(4L)) {
            int num = silentLoginService.checkLogin(username, password);
//            if(num != 0){
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "管理员用户登录4A系统失败，请联系系统管理员！");
//                throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
//            }
            AssertUtil.state(num == 0, "管理员用户登录4A系统失败，请联系系统管理员！");
            redisService.setCacheObject(username + "check", userInfo, 5L, TimeUnit.MINUTES);
            ajax.put("token", num);
            return ajax;
        } else
            passwordService.validate(user, password);

        if (user.getAccountType().equals(4L) && !isAdmin) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，管理员账号禁止外网访问");
            throw new ServiceException("对不起，管理员账号禁止外网访问");
        }
//        if (user.getAccountType().equals(3L)) {
//            QxUserVo item = loginUser.getUser();
//            String email = AESUtil.decryptCBC(item.getEmail(), item.getAesKey().substring(16), "****************");
//            String data = "";
//            try {
//                data = operationplatService.getInfoByEmail(email);
//            } catch (Exception ex) {
//                Assert.state(false, "能力支持获取用户失败，请联系系统管理员！");
//            }
//            // data = "{\"result\":{\"total\":1,\"current\":1,\"pages\":1,\"size\":10,\"optimizeCountSql\":true,\"records\":[{\"deptName\":\"中国移动通信集团广东有限公司/珠海分公司/产品研发珠海分中心\",\"jobStatus\":\"0\",\"gender\":\"M\",\"validDate\":\"2023-12-19T00:00:00\",\"orgPropType\":\"2\",\"propertity\":\"3\",\"companyOrgName\":\"珠海分公司\",\"organizeList\":[{\"managerLvlTypeName\":\"员工\",\"deptTypeName\":\"市场类\",\"viceManager\":\"\",\"deptName\":\"中国移动通信集团广东有限公司/珠海分公司/产品研发珠海分中心\",\"telephoneNumber\":\"\",\"positionType\":\"0\",\"busiUnitName\":\"中国移动集团公司广东公司\",\"orgManager\":\"\",\"orgPropType\":\"2\",\"validDate\":\"2022-01-27T00:00:00\",\"managerOrgCode\":\"\",\"postalCode\":\"\",\"propertity\":\"3\",\"companyOrgName\":\"珠海分公司\",\"orgLevelName\":\"三级半\",\"description\":\"\",\"admin\":\"\",\"orgLevel\":\"35\",\"source\":\"1\",\"orgSort\":\"23\",\"deptPropName\":\"地市公司-生产\",\"smapOrgManager\":\"\",\"orgCode\":\"2128100744\",\"statusName\":\"有效\",\"orgStyle\":\"2\",\"company\":\"2128\",\"expireDate\":\"2099-12-31T00:00:00\",\"superVisor\":\"\",\"deptCodeLevel\":\"/3021100001/2128100230/2128100744\",\"orgName\":\"产品研发珠海分中心\",\"companyOrgCode\":\"2128100230\",\"updateTime\":\"2023-06-28T10:30:01\",\"deptType\":\"20\",\"busiUnitCode\":\"CM021\",\"smapViceManager\":\"<EMAIL>\",\"areaCode\":\"广东省\",\"orgDuty\":\" \",\"postalAddress\":\"\",\"createTime\":\"2022-09-14T18:16:02\",\"duty\":\"\",\"deptProp\":\"270\",\"smapOrgCode\":\"\",\"sourceName\":\"统一用户\",\"upOrgCode\":\"2128100230\",\"managerLvlType\":\"55\",\"remarks\":\"数据割接倒换\",\"status\":\"1\"}],\"source\":\"1\",\"superUser\":\"0\",\"activateStatusName\":\"已激活\",\"backupStaffCode\":\"21040616\",\"uid\":\"8889165767\",\"jobStatusName\":\"在职\",\"deptPropName\":\"地市公司-生产\",\"orgCode\":\"2128100744\",\"staffName\":\"李宇桓\",\"statusName\":\"有效\",\"expireDate\":\"2099-12-31T00:00:00\",\"activateStatus\":\"1\",\"email\":\"<EMAIL>\",\"deptCodeLevel\":\"/3021100001/2128100230/2128100744\",\"staffCode\":\"E0021040616\",\"companyOrgCode\":\"2128100230\",\"updateTime\":\"2023-12-28T02:30:08\",\"sort\":100070,\"phone\":\"Kv5AhN7Cn8491mXGGMOS7HjVxPvOLh3hRt5n5t1r37IkIK+9JxYnbFAuR5avitGu731WO2786/QxDRCw1yTp/DeRwojy1eqLxpF9ssrPoDGiQhCPDVcEAkOt3Oz8eZiLkfV1JH8+UZNM2B0+ZS1lvyKbPPq8plFGcsDhkNOuGUY=\",\"createTime\":\"2022-09-17T19:55:24\",\"deptProp\":\"270\",\"sourceName\":\"统一用户\",\"managerLvlType\":\"55\",\"remarks\":\"数据割接倒换\",\"status\":\"1\"}],\"searchCount\":true,\"orders\":[]},\"code\":\"0\",\"message\":\"OK\"}";
//            JSONObject object = JSON.parseObject(data);
//            if (object.get("code").toString().equals("0")) {
//                JSONObject result = object.getJSONObject("result");
//                JSONArray array = result.getJSONArray("records");
//                Assert.state(array.size() > 0, "能力支持获取用户失败，请联系系统管理员！");
//                JSONObject jsonObject = array.getJSONObject(0);
//                Integer status = jsonObject.getInteger("status");
//                Integer jobStatus = jsonObject.getInteger("jobStatus");
//                if (status == 0 || jobStatus == 0) {
//                    QxUserCustomer qxUserCustomer = new QxUserCustomer();
//                    qxUserCustomer.setShareJobStatus(jobStatus);
//                    qxUserCustomer.setShareStatus(status);
//                    qxUserCustomer.setId(item.getId());
//                    iQxUserCustomerService.updateQxUserCustomer(qxUserCustomer);
//                }
//                Assert.state(status == 1, "能力支持获取用户状态为停用，请联系系统管理员！");
//                Assert.state(jobStatus == 1, "能力支持获取用户状态为离职，请联系系统管理员！");
//                System.out.println(jsonObject);
//            } else
//                Assert.state(false, "能力支持获取用户失败，请联系系统管理员！");
//
//        }


//        if (UserStatus.DISABLE.getCode().equals(user()))
//        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
//            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
//        }

        recordLogService.recordLogininforSuccess(username, Constants.LOGIN_SUCCESS, "登录成功",user.getRegion());
        ajax.put("token", tokenService.createToken(userInfo).get("access_token"));
//        ajax.put("user", user);
//        ajax.put("roles", userResult.getData().);
//        ajax.put("permissions", permissions);
        ajax.put("url", userResult.getData().getUrl());
        return ajax;
    }

    public void logout(String loginName) {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.ERROR_CODE == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
    }
}
