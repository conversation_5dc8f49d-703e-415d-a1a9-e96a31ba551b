/**
 * WsInterface.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package mypackage;

public interface WsInterface extends java.rmi.Remote {
    public java.lang.String applySmsCode(java.lang.String data) throws java.rmi.RemoteException;
    public java.lang.String getTicket(java.lang.String data) throws java.rmi.RemoteException;
    public java.lang.String checkSmsCode(java.lang.String data) throws java.rmi.RemoteException;
    public java.lang.String saveLog(java.lang.String data) throws java.rmi.RemoteException;
    public java.lang.String getSaltByUserCode(java.lang.String data) throws java.rmi.RemoteException;
    public java.lang.String checkUser(java.lang.String data) throws java.rmi.RemoteException;
}
