package com.qx.gateway.filter;

import cn.hutool.core.util.StrUtil;
import com.qx.common.core.constant.*;
import com.qx.gateway.config.properties.IgnoreWhiteProperties;
import com.qx.gateway.config.properties.SensitiveEndpointProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import com.qx.common.core.utils.JwtUtils;
import com.qx.common.core.utils.ServletUtils;
import com.qx.common.core.utils.StringUtils;
import com.qx.common.redis.service.RedisService;
import io.jsonwebtoken.Claims;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * 网关鉴权
 *
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered
{
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    // 敏感端点配置
    @Autowired
    private SensitiveEndpointProperties sensitiveEndpoint;

    @Autowired
    private RedisService redisService;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain)
    {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();

        String url = request.getURI().getPath();

        // 强制拦截敏感的actuator端点，即使在白名单中也要进行鉴权
        if (isSensitiveActuatorEndpoint(url))
        {
            log.warn("[敏感端点访问]请求路径:{}, 需要鉴权", url);
            // 不跳过鉴权，继续执行下面的token验证逻辑
        }
        // 跳过不需要验证的路径
        else if (StringUtils.matches(url, ignoreWhite.getWhites()))
        {
            return chain.filter(exchange);
        }
        String token = getToken(request);

        if (StringUtils.isEmpty(token))
        {
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        Claims claims =null;
        try {
             claims = JwtUtils.parseToken(token);
        }catch (Exception ex){
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确");
        }
        if (claims == null)
        {
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
        }
        String userkey = JwtUtils.getUserKey(claims);
        boolean islogin = redisService.hasKey(getTokenKey(userkey));

        if (StrUtil.startWith(url, Constants.SERVICE_URL_API)) {
            islogin = redisService.hasKey(getApiTokenKey(userkey));
        }
        else if (StrUtil.startWith(url, Constants.SERVICE_URL_SAPI)) {
            islogin = redisService.hasKey(getApiTokenKey(userkey));
        }
        else if (StrUtil.startWith(url, Constants.SERVICE_URL_EBP_API)) {
            islogin = redisService.hasKey(getApiTokenKey(userkey));
        }else {
            islogin = redisService.hasKey(getTokenKey(userkey));
        }

        if (!islogin)
        {
            return unauthorizedResponse(exchange, "登录状态已过期");
        }
        String userid = JwtUtils.getUserId(claims);
        String username = JwtUtils.getUserName(claims);
        if (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username))
        {
            return unauthorizedResponse(exchange, "令牌验证失败");
        }

        // 设置用户信息到请求
        addHeader(mutate, SecurityConstants.USER_KEY, userkey);
        addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userid);
        addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
        // 内部请求来源参数清除
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);

        redisService.redisTemplate.boundValueOps(CacheConstants.TASK_CONCURRENCY_COUNT).increment();
        redisService.expire(CacheConstants.TASK_CONCURRENCY_COUNT,2L, TimeUnit.SECONDS);
        redisService.redisTemplate.boundValueOps(CacheConstants.TASK_P_M_CONCURRENCY_COUNT).increment();
        redisService.expire(CacheConstants.TASK_P_M_CONCURRENCY_COUNT,1L, TimeUnit.MINUTES);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value)
    {
        if (value == null)
        {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name)
    {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg)
    {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNAUTHORIZED);
    }

    /**
     * 获取缓存key
     */
    private String getTokenKey(String token)
    {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }
    private String getApiTokenKey(String token)
    {
        return CacheConstants.LOGIN_TOKEN_KEY_API + token;
    }



    /**
     * 判断是否为敏感的actuator端点
     */
    private boolean isSensitiveActuatorEndpoint(String url)
    {
        // 检查配置的敏感端点列表
        for (String endpoint : sensitiveEndpoint.getEndpoints())
        {
            if (url.equals(endpoint) || url.startsWith(endpoint + "/"))
            {
                return true;
            }
        }

        // 对于actuator端点，如果不在允许列表中，则认为是敏感的
        if (url.startsWith("/actuator/"))
        {
            return !isAllowedActuatorEndpoint(url);
        }

        return false;
    }

    /**
     * 判断是否为允许的actuator端点（如健康检查等）
     */
    private boolean isAllowedActuatorEndpoint(String url)
    {
        for (String endpoint : sensitiveEndpoint.getAllowedEndpoints())
        {
            if (url.equals(endpoint) || url.startsWith(endpoint + "/"))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request)
    {
        String token = request.getHeaders().getFirst(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    @Override
    public int getOrder()
    {
        return -200;
    }
}
