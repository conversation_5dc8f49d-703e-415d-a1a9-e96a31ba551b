<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-bootstrap:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.13" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.33" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-rsa:1.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcpkix-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcutil-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.33" level="project" />
  </component>
</module>