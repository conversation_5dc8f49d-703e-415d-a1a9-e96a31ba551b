10:40:17.046 [reactor-http-nio-2] ERROR c.q.g.f.<PERSON>th<PERSON>er - [unauthorizedResponse,128] - [鉴权异常处理]请求路径:/api/home/<USER>
11:32:44.241 [reactor-http-nio-10] ERROR c.q.g.f.AuthFilter - [unauthorizedResponse,128] - [鉴权异常处理]请求路径:/system/user/getInfo
15:00:39.839 [reactor-http-nio-12] ERROR c.q.g.f.AuthFilter - [unauthorizedResponse,128] - [鉴权异常处理]请求路径:/system/user/getInfo
15:01:09.587 [reactor-http-nio-4] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/home/<USER>/manage/list,异常信息:404 NOT_FOUND
15:07:28.648 [boundedElastic-77] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/system/user/getInfo,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for qx-system"
15:07:54.823 [reactor-http-nio-4] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/home/<USER>/manage/list,异常信息:404 NOT_FOUND
15:08:00.497 [reactor-http-nio-5] ERROR c.q.g.h.GatewayExceptionHandler - [handle,51] - [网关异常处理]请求路径:/home/<USER>/manage/list,异常信息:404 NOT_FOUND
16:36:14.847 [reactor-http-nio-9] ERROR c.q.g.f.AuthFilter - [unauthorizedResponse,128] - [鉴权异常处理]请求路径:/system/user/getInfo
