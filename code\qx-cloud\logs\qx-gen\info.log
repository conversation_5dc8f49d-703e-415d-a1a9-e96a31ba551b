10:52:50.740 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:52:51.400 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:52:51.400 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:52:55.586 [main] INFO  c.q.g.RuoYiGenApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:52:58.053 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
10:52:58.053 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
10:52:58.053 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
10:52:58.058 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
10:52:58.071 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9202"]
10:52:58.073 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:52:58.073 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
10:52:58.428 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:52:58.532 [main] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
11:03:02.657 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:03:02.957 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:03:02.957 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:03:05.909 [main] INFO  c.q.g.RuoYiGenApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:03:07.670 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
11:03:07.670 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
11:03:07.670 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
11:03:07.674 [main] INFO  o.a.c.c.AprLifecycleListener - [log,168] - OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
11:03:07.685 [main] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-9202"]
11:03:07.686 [main] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:03:07.686 [main] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.106]
11:03:07.831 [main] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:03:07.919 [main] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
