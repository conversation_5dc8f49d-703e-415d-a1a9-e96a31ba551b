package com.qx.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.qx.common.security.annotation.EnableRyFeignClients;

/**
 * 认证授权中心
 *
 * <AUTHOR>
 */
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class RuoYiAuthApplication
{
    public static void main(String[] args)
    {
        System.setProperty("sun.io.useCanonCaches", "false");
        SpringApplication.run(RuoYiAuthApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  认证授权中心启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " d=====(￣▽￣*)b 项       \n" +
                " d=====(￣▽￣*)b 目       \n" +
                " d=====(￣▽￣*)b 启       \n" +
                " d=====(￣▽￣*)b 动       \n" +
                " d=====(￣▽￣*)b 成       \n" +
                " d=====(￣▽￣*)b 功       \n" +
                " d=====(￣▽￣*)b 顶       \n" +
                " d=====(￣▽￣*)b 顶");
    }
}
