package com.qx.system.api.factory;

import com.qx.common.core.domain.R;
import com.qx.common.core.web.domain.AjaxResult;
import com.qx.common.core.web.domain.entity.QxUserCustmoerTo;
import com.qx.common.core.web.domain.entity.SysUser;
import com.qx.common.core.web.domain.model.LoginUser;
import com.qx.system.api.RemoteUserApiService;
import com.qx.system.api.RemoteUserService;
import com.qx.system.api.model.MobileToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUserApiFallbackFactory implements FallbackFactory<RemoteUserApiService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserApiFallbackFactory.class);

    @Override
    public RemoteUserApiService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserApiService()
        {
            @Override
            public QxUserCustmoerTo getOnlineLogin(String token, Integer isMobile, String timeMd5,String source)
            {
                QxUserCustmoerTo qxUserCustmoerTo=new QxUserCustmoerTo();
                qxUserCustmoerTo.setMsg("登录失败:" + throwable.getMessage());
                qxUserCustmoerTo.setCode(500);
                return qxUserCustmoerTo;
            }

            @Override
            public QxUserCustmoerTo getOnlineLoginWd(String userId,
                                                   String source)
            {
                QxUserCustmoerTo qxUserCustmoerTo=new QxUserCustmoerTo();
                qxUserCustmoerTo.setMsg("登录失败:" + throwable.getMessage());
                qxUserCustmoerTo.setCode(500);
                return qxUserCustmoerTo;
            }
        };
    }
}
