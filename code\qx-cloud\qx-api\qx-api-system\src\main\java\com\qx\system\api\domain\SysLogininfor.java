package com.qx.system.api.domain;

import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qx.common.core.annotation.Excel;
import com.qx.common.core.annotation.Excel.ColumnType;
import com.qx.common.core.web.domain.BaseEntity;

/**
 * 系统访问记录表 sys_logininfor
 *
 * <AUTHOR>
 */
public class SysLogininfor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private List<Long> exportIdList;
    @ExcelIgnore
    private String nickName;

    /**
     * ID
     */
    @Excel(name = "序号", cellType = ColumnType.NUMERIC)
    @ExcelProperty("序号")
    private Long infoId;

    /**
     * 用户账号
     */
    @Excel(name = "用户账号")
    @ExcelProperty("用户账号")
    private String userName;

    /**
     * 状态 0成功 1失败
     */
    @ExcelIgnore
    @Excel(name = "状态", readConverterExp = "0=成功,1=失败")
    private String status;

    @ExcelProperty("状态")
    private String statusDesc;

    /**
     * 地址
     */
    @Excel(name = "地址")
    @ExcelProperty("地址")
    private String ipaddr;


    /**
     * 登录地点
     */
    @Excel(name = "登录地点")
    @ExcelProperty("登录地点")
    private String loginLocation;

    /**
     * 浏览器类型
     */
    @Excel(name = "浏览器")
    @ExcelProperty("浏览器")
    private String browser;

    /**
     * 操作系统
     */
    @Excel(name = "操作系统")
    @ExcelProperty("操作系统")
    private String os;

    /**
     * 描述
     */
    @Excel(name = "描述")
    @ExcelProperty("描述")
    private String msg;

    /**
     * 访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("访问时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date loginTime;


    /**
     * 提示消息
     */
    @Excel(name = "登录类型")
    @ExcelProperty("登录类型")
    private Integer loginType;

    /**
     * 区域
     */
    private String region;

    public List<Long> getExportIdList() {
        return exportIdList;
    }

    public void setExportIdList(List<Long> exportIdList) {
        this.exportIdList = exportIdList;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getStatusDesc() {
        return "0".equals(status) ? "成功" : "失败";
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getLoginType() {
        return loginType;
    }

    public void setLoginType(Integer loginType) {
        this.loginType = loginType;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIpaddr() {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr) {
        this.ipaddr = ipaddr;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }
}
