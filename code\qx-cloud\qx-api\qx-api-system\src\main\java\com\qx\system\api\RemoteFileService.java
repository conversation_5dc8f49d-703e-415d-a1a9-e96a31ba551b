package com.qx.system.api;

import com.qx.system.api.domain.SysFile;
import com.qx.system.api.factory.RemoteFileFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.qx.common.core.constant.ServiceNameConstants;
import com.qx.common.core.domain.R;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "file-service", url = "${file-service.url}", fallbackFactory = RemoteFileFallbackFactory.class)
public interface RemoteFileService
{
    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysFile> upload(@RequestPart(value = "file") MultipartFile file);

    /**
     * 上传文件
     *
     * @param path 文件路径
     * @return 结果
     */
    @GetMapping(value = "/panorama")
    public  R<Boolean> panorama(@RequestParam("path") String path);
}
