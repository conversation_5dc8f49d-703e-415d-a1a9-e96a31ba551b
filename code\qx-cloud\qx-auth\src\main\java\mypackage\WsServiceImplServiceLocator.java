/**
 * WsServiceImplServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package mypackage;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class WsServiceImplServiceLocator extends org.apache.axis.client.Service implements mypackage.WsServiceImplService {

    @Value("${chinamobile.noninductive}")
    private String noninductive = "";

    public WsServiceImplServiceLocator() {
    }


    public WsServiceImplServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public WsServiceImplServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for WsServiceImplPort
    private java.lang.String WsServiceImplPort_address = noninductive;

    public java.lang.String getWsServiceImplPortAddress() {
        return WsServiceImplPort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String WsServiceImplPortWSDDServiceName = "WsServiceImplPort";

    public java.lang.String getWsServiceImplPortWSDDServiceName() {
        return WsServiceImplPortWSDDServiceName;
    }

    public void setWsServiceImplPortWSDDServiceName(java.lang.String name) {
        WsServiceImplPortWSDDServiceName = name;
    }

    public mypackage.WsInterface getWsServiceImplPort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WsServiceImplPort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWsServiceImplPort(endpoint);
    }

    public mypackage.WsInterface getWsServiceImplPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            mypackage.WsServiceImplServiceSoapBindingStub _stub = new mypackage.WsServiceImplServiceSoapBindingStub(portAddress, this);
            _stub.setPortName(getWsServiceImplPortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWsServiceImplPortEndpointAddress(java.lang.String address) {
        WsServiceImplPort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (mypackage.WsInterface.class.isAssignableFrom(serviceEndpointInterface)) {
                mypackage.WsServiceImplServiceSoapBindingStub _stub = new mypackage.WsServiceImplServiceSoapBindingStub(new java.net.URL(WsServiceImplPort_address), this);
                _stub.setPortName(getWsServiceImplPortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("WsServiceImplPort".equals(inputPortName)) {
            return getWsServiceImplPort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://server.webservice.example.com", "WsServiceImplService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://server.webservice.example.com", "WsServiceImplPort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {

if ("WsServiceImplPort".equals(portName)) {
            setWsServiceImplPortEndpointAddress(address);
        }
        else
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
